# SharePoint Integration Guide for RAG Application

## Phase 1: Azure AD Application Registration

### Step 1: Register a New Application in Azure AD

1. **Log in to Azure Portal**
   - Go to https://portal.azure.com
   - Sign in with company administrator account

2. **Navigate to App Registrations**
   - Click on "Azure Active Directory" in left sidebar
   - Select "App registrations"
   - Click "+ New registration"

3. **Complete Registration Form**
   - Name: "RAG SharePoint Integration"
   - Supported account types: "Accounts in this organizational directory only"
   - Redirect URI: Web - http://localhost:8080/auth_callback (for development)

### Step 2: Configure Authentication

1. **Authentication Settings**
   - Go to "Authentication" in left menu
   - Under "Implicit grant and hybrid flows":
     - Check "ID tokens"
   - Under "Advanced settings":
     - Set "Allow public client flows" to "No"

### Step 3: Create Client Secret

1. **Generate Secret**
   - Go to "Certificates & secrets"
   - Create new client secret
   - Store the secret value securely (only shown once)

### Step 4: Configure API Permissions

1. **Required Permissions**
   - Files.Read
   - Files.Read.All
   - Sites.Read.All
   - Grant admin consent

### Step 5: Important IDs
- Application (client) ID
- Directory (tenant) ID

## Phase 2: Code Configuration

### Environment Variables
```env
MS_CLIENT_ID=your-application-id
MS_CLIENT_SECRET=your-client-secret
MS_TENANT_ID=your-tenant-id
SESSION_SECRET=your-session-secret
```

### Required Dependencies
```python
msal
requests
Flask-Session
```

### Key Features to Implement

1. **Authentication Flow**
   - Microsoft MSAL authentication
   - Token management
   - Session handling

2. **SharePoint Integration**
   - Site listing
   - Document library browsing
   - File access and download

3. **UI Components**
   - Site selection
   - Document library navigation
   - File browser
   - Import functionality

### Security Considerations

1. **Token Management**
   - Secure token storage
   - Token refresh handling
   - Session management

2. **Access Control**
   - Permission validation
   - Scope verification
   - Error handling

## Phase 3: Testing and Deployment

### Local Testing
1. Configure environment variables
2. Test authentication flow
3. Verify file access
4. Test import functionality

### Production Deployment
1. Update production environment variables
2. Configure production redirect URIs
3. Deploy with proper security measures

## Migration Notes

### From GCS to SharePoint
1. Update file upload endpoints
2. Modify file retrieval logic
3. Adapt storage patterns
4. Update UI components

### Data Handling
1. File metadata management
2. Content indexing
3. Search functionality adaptation

## Monitoring and Maintenance

1. **Authentication Monitoring**
   - Token refresh tracking
   - Authentication failures
   - Session management

2. **Performance Monitoring**
   - API response times
   - File transfer speeds
   - Error rates

3. **Regular Maintenance**
   - Token rotation
   - Permission audits
   - Security updates 