[build]
builder = "nixpacks"
buildCommand = "pip install -r requirements.txt"

[deploy]
startCommand = "python -c \"import os; port = int(os.getenv('PORT', '8082')); from subprocess import run; run(['python', '-m', 'uvicorn', 'app:app', '--host', '0.0.0.0', '--port', str(port), '--workers', '1'])\""
restartPolicyType = "on_failure"
healthcheckPath = "/health"
healthcheckTimeout = 100
healthcheckInterval = 15
restartPolicyMaxRetries = 3

[service]
autoDeploy = true
internal_port = 8082

[phases.setup]
nixPkgs = ["file", "libmagic"]

[deploy.env]
PYTHON_VERSION = "3.9.0"
ADMIN_USERNAME = "admin"
FILE_SIZE_LIMIT = "104857600"
OPENAI_MODEL = "gpt-4-turbo-preview"
CHUNK_SIZE = "512"
CHUNK_OVERLAP = "50"
SIMILARITY_TOP_K = "20"
CORS_ORIGINS = "http://localhost:8082,http://0.0.0.0:8082,https://*.onrender.com,https://*.railway.app"

[deploy.healthcheck]
path = "/health"
interval = "15s"
timeout = "10s"
retries = 3 