import os
import sys
import json
import logging
import secrets
import uuid  # Add UUID for state generation
from datetime import datetime, timedelta
from typing import List, Optional, Union
from fastapi import (
    FastAPI,
    File,
    UploadFile,
    Request,
    HTTPException,
    Depends,
    Query,
    Form,
    status,
)
from fastapi.responses import JSONResponse, StreamingResponse, RedirectResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.security import HTTPBasic, HTTPBasicCredentials
from starlette.middleware.sessions import SessionMiddleware
import tenacity
import mimetypes
from pathlib import Path
import tempfile
import traceback
import asyncio
from concurrent.futures import ThreadPoolExecutor
from dotenv import load_dotenv
from urllib.parse import unquote
from sharepoint_client import SharePointClient
from config import settings
import msal
import aiohttp
import pytesseract
from PIL import Image
import io
import shutil
import openai

# Try to import PyPDF2, but don't fail if it's not available
try:
    import PyPDF2

    PYPDF2_AVAILABLE = True
except ImportError:
    PYPDF2_AVAILABLE = False
    logging.warning(
        "PyPDF2 not available. PDF processing will use fallback methods only."
    )

from llama_index.core import (
    VectorStoreIndex,
    SimpleDirectoryReader,
    StorageContext,
    load_index_from_storage,
    Settings as LlamaSettings,
    get_response_synthesizer,
)
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.llms.openai import OpenAI as LlamaOpenAI
from llama_index.postprocessor.cohere_rerank import CohereRerank
from llama_index.core.node_parser import SentenceSplitter
from llama_index.core.retrievers import VectorIndexRetriever
from llama_index.core.query_engine import RetrieverQueryEngine
from llama_index.core.schema import MetadataMode
from llama_index.core.schema import TextNode

# Load environment variables first
load_dotenv()

# Add this near the top with other global variables
background_tasks = set()

# Near the top with other settings
OPENAI_MODEL = "gpt-3.5-turbo"  # Changed from gpt-4-turbo-preview to gpt-3.5-turbo

# Configure logging BEFORE using the logger
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger(__name__)

# Define port and host consistently (read from settings or use defaults)
APP_HOST = (
    settings.HOST if settings.HOST else "127.0.0.1"
)  # Use from settings or default (Used for server binding)
APP_PORT = settings.PORT if settings.PORT else 8082  # Use from settings or default

# <<< Construct Redirect URI Base based on environment >>>
RENDER_EXTERNAL_URL = os.getenv("RENDER_EXTERNAL_URL")
if RENDER_EXTERNAL_URL:  # Running on Render
    REDIRECT_URI_BASE = RENDER_EXTERNAL_URL
    logger.info(
        f"Detected Render environment. Using REDIRECT_URI_BASE: {REDIRECT_URI_BASE}"
    )
else:  # Running locally
    REDIRECT_URI_BASE = f"http://localhost:{APP_PORT}"
    logger.info(f"Running locally. Using REDIRECT_URI_BASE: {REDIRECT_URI_BASE}")

# ----- MSAL Configuration for Delegated Flow -----
MSAL_SCOPES = [
    "User.Read",
    "Sites.Read.All",
    "Files.Read.All",
    "Sites.ReadWrite.All",  # Adjust as needed
]
# Build the MSAL app instance used for user authentication
msal_app = None
if settings.MS_CLIENT_ID and settings.MS_CLIENT_SECRET and settings.MS_TENANT_ID:
    msal_app = msal.ConfidentialClientApplication(
        settings.MS_CLIENT_ID,
        authority=f"https://login.microsoftonline.com/{settings.MS_TENANT_ID}",
        client_credential=settings.MS_CLIENT_SECRET.get_secret_value(),
    )
else:
    logger.warning(
        "Microsoft credentials (MS_CLIENT_ID, MS_CLIENT_SECRET, MS_TENANT_ID) not configured. SharePoint delegated auth will not work."
    )

REDIRECT_PATH = "/auth/callback/microsoft"
# <<< This will now correctly use http://localhost:8082/... again >>>
EXPECTED_REDIRECT_URI = f"{REDIRECT_URI_BASE}{REDIRECT_PATH}"
# ------------------------------------------------

# Initialize SharePoint client (it no longer needs credentials itself)
sharepoint_client = None
if settings.USE_SHAREPOINT:
    try:  # Add try-except for robustness
        sharepoint_client = SharePointClient()
        logger.info("SharePointClient initialized (will use user tokens).")
    except Exception as e:
        logger.error(f"Failed to initialize SharePointClient: {e}")
        sharepoint_client = None  # Ensure it's None if init fails
else:
    logger.info("SharePoint integration is disabled (USE_SHAREPOINT=False).")


def format_response(text):
    """Format the response text with proper markdown and consistent formatting."""
    # Test case to verify formatting
    if text.startswith("TEST_FORMAT:"):
        text = """DDB AI Labs offers a range of AI-powered services focused on AI Development and AI Creativity.

For AI Development, the services include:
Creation and implementation of artificial intelligence in various applications such as chatbots, AI integrations into apps and websites, development of customized AI agents to boost productivity, and fine-tuning of AI models.

Tools provided for AI Development include:
OpenAI API - For integrating GPT-4 into applications
Llama 2 - Open source LLMs for building and training AI models
GitHub Copilot and Cursor - Code assistants for coding assistance
Relume Site Builder - For AI-powered website design
Framer AI - For automated website generation
Microsoft AutoGen - For creating and completing tasks autonomously"""

    # Split the text into paragraphs first (split by double newlines)
    paragraphs = text.split("\n\n")
    formatted_paragraphs = []

    for paragraph in paragraphs:
        # Split each paragraph into lines
        lines = paragraph.strip().split("\n")
        formatted_lines = []

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Check if line is a heading (ends with :)
            if line.endswith(":"):
                formatted_lines.append(f"\n**{line}**\n")
            # Check if line is a list item (starts with -)
            elif line.startswith("-"):
                formatted_lines.append(f"• {line[1:].strip()}")
            # Otherwise, treat as normal text
            else:
                formatted_lines.append(line)

        # Join the lines in this paragraph
        formatted_paragraph = "\n".join(formatted_lines)
        if formatted_paragraph:
            formatted_paragraphs.append(formatted_paragraph)

    # Join paragraphs with double newlines for better spacing
    return "\n\n".join(formatted_paragraphs)


# Add startup message with validated environment
logger.info("Starting application...")
logger.info(f"Python version: {sys.version}")
logger.info(f"Environment: {os.getenv('RAILWAY_ENVIRONMENT', 'development')}")

# Check for required API keys early
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
COHERE_API_KEY = os.getenv("COHERE_API_KEY")

if not OPENAI_API_KEY:
    print("ERROR: OPENAI_API_KEY environment variable is not set")
    raise ValueError("OPENAI_API_KEY environment variable is not set")
if not COHERE_API_KEY:
    print("ERROR: COHERE_API_KEY environment variable is not set")
    raise ValueError("COHERE_API_KEY environment variable is not set")

# Initialize FastAPI app
app = FastAPI()

# Initialize global variables
llm = None
embed_model = None
index = None  # Will be initialized in startup

# Add session middleware with secure secret key
app.add_middleware(
    SessionMiddleware,
    secret_key=settings.SESSION_SECRET_KEY.get_secret_value(),
    session_cookie="rag_session",
    max_age=3600 * 24 * 7,
    same_site="lax",
    path="/",
    # <<< Remove explicit domain - let browser use request host (127.0.0.1) >>>
    # domain="127.0.0.1",
    https_only=os.getenv("RAILWAY_ENVIRONMENT", "development") != "development",
)

# Configure CORS with settings
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origin_list,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["Content-Type", "text/event-stream"],
)

# Mount static files and templates
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")


# --- Basic Auth Endpoints and Helpers ---
async def get_current_user(request: Request):
    """Get current user from session."""
    session = request.session
    if "basic_user" not in session:
        raise HTTPException(
            status_code=401,
            detail="Not authenticated",
        )
    return session["basic_user"]


# Create security object
security = HTTPBasic()
BasicUserDep = Depends(get_current_user)


@app.on_event("startup")
async def startup_event():
    """Initialize application state and create required directories."""
    logger.info("Starting application with document persistence...")
    logger.info(f"Python version: {sys.version}")
    logger.info(f"Current working directory: {os.getcwd()}")
    logger.info(f"Environment: {os.getenv('ENV', 'development')}")

    # Create required directories
    try:
        settings.STORAGE_DIR.mkdir(exist_ok=True)
        settings.DATA_DIR.mkdir(exist_ok=True)
        settings.UPLOAD_DIR.mkdir(exist_ok=True)
        settings.TEMP_DIR.mkdir(exist_ok=True)
        logger.info("Storage directory ready")
    except Exception as e:
        logger.error(f"Error creating directories: {e}")
        # Continue anyway - the application might still work with in-memory operations

    # Initialize OpenAI components
    try:
        logger.info("Initializing OpenAI model...")
        openai.api_key = settings.OPENAI_API_KEY.get_secret_value()

        # Try to initialize with the preferred model
        try:
            app.state.openai_model = OPENAI_MODEL
            app.state.llm = LlamaOpenAI(
                model=app.state.openai_model, temperature=0.7, max_tokens=2000
            )
            logger.info(
                f"OpenAI LLM initialized successfully with model: {app.state.openai_model}"
            )
        except Exception as model_error:
            # If the preferred model fails, fallback to a more reliable one
            logger.warning(
                f"Failed to initialize with model {OPENAI_MODEL}: {model_error}"
            )
            logger.info("Attempting fallback to gpt-3.5-turbo model")

            fallback_model = "gpt-3.5-turbo"
            app.state.openai_model = fallback_model
            app.state.llm = LlamaOpenAI(
                model=fallback_model, temperature=0.7, max_tokens=1500
            )
            logger.info(f"OpenAI LLM initialized with fallback model: {fallback_model}")

        # Initialize embedding model
        logger.info("Initializing embedding model...")
        app.state.embed_model = OpenAIEmbedding()
        logger.info("Embedding model initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing OpenAI components: {e}")
        # Continue to other initializations - we'll handle missing components later

    # Configure global settings
    try:
        logger.info("Configuring global settings...")
        app.state.settings = settings
        logger.info("Global settings configured successfully")
    except Exception as e:
        logger.error(f"Error configuring settings: {e}")

    # Initialize Cohere reranker
    try:
        logger.info("Initializing Cohere reranker...")
        app.state.reranker = CohereRerank(
            api_key=settings.COHERE_API_KEY.get_secret_value()
        )
        logger.info("Cohere reranker initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing Cohere reranker: {e}")
        app.state.reranker = None  # Set to None so we can check later

    # Initialize SharePoint client if enabled
    if settings.USE_SHAREPOINT:
        try:
            logger.info("Initializing SharePoint client...")
            app.state.sharepoint_client = SharePointClient()
            logger.info("SharePoint client initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing SharePoint client: {e}")
            # Continue without SharePoint - the RAG can still work

    # Load vector index with fallbacks at each stage
    try:
        logger.info("Loading vector index...")
        try:
            app.state.index = await load_index()
            logger.info("Index loaded successfully")
        except Exception as e:
            logger.error(f"Error loading index: {e}")
            logger.warning("Creating empty index as fallback...")
            app.state.index = await create_empty_index()
            logger.info("Created empty fallback index")

        # Create query engine from the index
        try:
            app.state.query_engine = get_query_engine(
                app.state.index, app.state.reranker
            )
            logger.info("Query engine created successfully from loaded index.")
        except Exception as e:
            logger.error(f"Error creating query engine: {e}")
            # Will try to create on-demand when needed
    except Exception as e:
        logger.error(f"Critical error during index/query engine initialization: {e}")
        # The application will continue, but querying will not work until fixed

    logger.info("Startup completed successfully")


async def create_empty_index():
    """Create an empty index without documents."""
    try:
        logger.info("Creating new empty index...")
        # Use Settings instead of ServiceContext
        llm = LlamaOpenAI(
            model=OPENAI_MODEL,
            temperature=0.7,
            max_tokens=2000,
        )
        embed_model = OpenAIEmbedding(model="text-embedding-3-small")

        LlamaSettings.llm = llm
        LlamaSettings.embed_model = embed_model

        # Create an empty vector store index
        index = VectorStoreIndex([])

        # Persist the empty index to storage
        storage_context = index.storage_context
        storage_context.persist(persist_dir=str(settings.STORAGE_DIR))
        logger.info("Successfully created and persisted empty index")

        return index
    except Exception as e:
        logger.error(f"Error creating empty index: {str(e)}")
        # Return a non-persisted index as fallback
        logger.warning("Creating non-persisted fallback index")
        return VectorStoreIndex([])


def get_query_engine(index, reranker=None):
    """Create a query engine from the index and reranker."""
    try:
        logger.info("Setting up query engine...")
        # Create retriever with increased similarity_top_k
        retriever = VectorIndexRetriever(
            index=index,
            similarity_top_k=10,  # Increased from default
        )
        logger.info("Created vector index retriever")

        # Create response synthesizer with more detailed configuration
        response_synthesizer = get_response_synthesizer(
            response_mode="tree_summarize",  # Using tree summarization for better structured responses
            verbose=True,
            streaming=False,
        )
        logger.info("Created response synthesizer")

        # Create query engine
        query_engine = RetrieverQueryEngine(
            retriever=retriever,
            response_synthesizer=response_synthesizer,
            node_postprocessors=[reranker] if reranker else [],
        )
        logger.info("Query engine created successfully")
        return query_engine
    except Exception as e:
        logger.error(f"Error creating query engine: {str(e)}")
        return None


@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup resources on application shutdown."""
    if hasattr(app.state, "index"):
        try:
            logger.info("Persisting index to storage...")
            app.state.index.storage_context.persist(
                persist_dir=str(settings.STORAGE_DIR)
            )
            logger.info("Index persisted successfully")
        except Exception as e:
            logger.error(f"Error persisting index: {e}")


# --- Basic Auth Endpoints and Helpers ---
async def get_microsoft_access_token(request: Request) -> str:
    """Get Microsoft Graph API access token from session."""
    logger.info("Attempting to get Microsoft access token...")

    # Check if token cache and user object exist in session
    session = request.session
    token_cache_exists = "ms_token_cache" in session
    ms_user_exists = "ms_user" in session
    logger.info(
        f"Session check: Found token_cache: {token_cache_exists}, Found ms_user: {ms_user_exists}"
    )

    # Log session data for debugging (omit sensitive values)
    session_keys = list(session.keys())
    logger.info(f"Session keys: {session_keys}")

    if "basic_user" in session:
        logger.info("Basic user authentication is present in session")
    else:
        logger.warning("No basic user authentication in session")

    if not token_cache_exists or not ms_user_exists:
        # No token in session, redirect to Microsoft login
        logger.warning(
            "No Microsoft token cache found in session. Redirecting to login."
        )
        # Check if this is a web browser request (needs redirect) or API call (needs 401)
        if request.url.path.startswith("/api/"):
            # For API requests, return a 401 with clear error
            raise HTTPException(
                status_code=401,
                detail="Microsoft authentication required",
            )
        else:
            # For browser requests, redirect to Microsoft login page
            redirect_url = request.url_for("login_microsoft")
            logger.info(f"Redirect URL: {redirect_url}")
            return RedirectResponse(url=str(redirect_url), status_code=302)

    # Get cached token
    cached_token = session.get("ms_token_cache", {})
    access_token = cached_token.get("access_token")

    # Log token information (without exposing the actual token)
    if access_token:
        logger.info(f"Access token found in session (length: {len(access_token)})")
    else:
        logger.error("Access token is missing from ms_token_cache")

    if not access_token:
        logger.error("Access token is empty or invalid in session")
        # Clear invalid session data
        if "ms_token_cache" in session:
            del session["ms_token_cache"]
        if "ms_user" in session:
            del session["ms_user"]

        # Redirect to login
        redirect_url = request.url_for("login_microsoft")
        logger.info(f"Redirect URL for invalid token: {redirect_url}")
        return RedirectResponse(url=str(redirect_url), status_code=302)

    # Quick validation test (just check token format)
    if not access_token.startswith("eyJ") or len(access_token) < 100:
        logger.warning(f"Token appears to be in invalid format, redirecting to login")
        redirect_url = request.url_for("login_microsoft")
        return RedirectResponse(url=str(redirect_url), status_code=302)

    return access_token


MsTokenDep = Depends(get_microsoft_access_token)


# --- Basic Auth Endpoints ---
@app.post("/login")
async def login(
    request: Request, credentials: HTTPBasicCredentials = Depends(security)
):
    # Verify credentials against settings (environment variables)
    correct_username = settings.USERNAME
    correct_password = settings.PASSWORD.get_secret_value()

    # Simple credential validation
    if not (
        credentials.username == correct_username
        and credentials.password == correct_password
    ):
        logger.warning(f"Failed login attempt for user: {credentials.username}")
        raise HTTPException(
            status_code=401,
            detail="Invalid credentials",
            headers={"WWW-Authenticate": "Basic"},
        )

    # Set user in session
    session = request.session
    session["basic_user"] = {
        "username": credentials.username,
        "authenticated": True,
    }
    logger.info(f"User '{credentials.username}' successfully logged in")

    # Return success
    return {"authenticated": True, "username": credentials.username}


@app.post("/logout")
async def logout(request: Request):
    # Clear the session
    session = request.session
    session.clear()
    logger.info("User logged out")
    return {"message": "Successfully logged out"}


@app.get("/user")
async def get_user_status(request: Request):
    # Get user from session or return unauthenticated
    session = request.session
    if "basic_user" in session:
        return session["basic_user"]
    return {"authenticated": False}


@app.get("/login/microsoft", name="login_microsoft")
async def login_microsoft(request: Request, user=BasicUserDep):
    """Initiate Microsoft OAuth flow."""
    try:
        if not msal_app:
            logger.error("Microsoft authentication is not configured")
            return templates.TemplateResponse(
                "error.html",
                {
                    "request": request,
                    "error_title": "Microsoft Authentication Not Configured",
                    "error_message": "Microsoft authentication is not properly configured.",
                    "error_details": "Please check your environment variables: MS_CLIENT_ID, MS_CLIENT_SECRET, and MS_TENANT_ID",
                },
                status_code=500,
            )

        # Ensure we have a basic user session
        session = request.session
        if "basic_user" not in session:
            logger.error("No basic user session found during Microsoft login")
            return templates.TemplateResponse(
                "error.html",
                {
                    "request": request,
                    "error_title": "Authentication Required",
                    "error_message": "Please log in first.",
                    "error_details": "You must be logged in to access SharePoint.",
                },
                status_code=401,
            )

        # Generate state for CSRF protection
        state = str(uuid.uuid4())
        session["ms_auth_state"] = state

        # Generate the authorization URL
        auth_uri = msal_app.get_authorization_request_url(
            MSAL_SCOPES,
            state=state,
            redirect_uri=EXPECTED_REDIRECT_URI,  # Use the correctly constructed URI
        )

        # <<< Log the generated URL BEFORE redirecting >>>
        logger.info(f"Generated Microsoft auth URL for redirect: {auth_uri}")

        return RedirectResponse(auth_uri, status_code=307)

    except Exception as e:
        logger.error(f"Error during Microsoft login initiation: {e}", exc_info=True)
        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_title": "Microsoft Login Error",
                "error_message": "Failed to initiate Microsoft login.",
                "error_details": f"Error details: {str(e)}",
            },
            status_code=500,
        )


@app.get(REDIRECT_PATH, name="auth_callback_microsoft")
async def auth_callback_microsoft(
    request: Request, code: str = Query(...), state: str = Query(...)
):
    """Handle the Microsoft OAuth callback."""
    try:
        if not msal_app:
            logger.error("Microsoft authentication is not configured")
            return JSONResponse(
                content={
                    "error": "Microsoft Authentication Not Configured",
                    "message": "Microsoft authentication is not properly configured.",
                    "details": "Please check your environment variables: MS_CLIENT_ID, MS_CLIENT_SECRET, and MS_TENANT_ID",
                },
                status_code=500,
            )

        # Verify state to prevent CSRF
        session = request.session
        stored_state = session.get("ms_auth_state")
        if not stored_state or stored_state != state:
            logger.error("State mismatch in Microsoft auth callback")
            return JSONResponse(
                content={
                    "error": "Authentication Error",
                    "message": "Invalid authentication state.",
                    "details": "The authentication request may have expired or been tampered with. Please try again.",
                },
                status_code=400,
            )

        # Exchange the auth code for tokens
        result = msal_app.acquire_token_by_authorization_code(
            code, scopes=MSAL_SCOPES, redirect_uri=EXPECTED_REDIRECT_URI
        )

        if "error" in result:
            logger.error(f"Token acquisition failed: {result.get('error_description')}")
            return JSONResponse(
                content={
                    "error": "Microsoft Authentication Failed",
                    "message": "Failed to complete Microsoft authentication.",
                    "details": result.get(
                        "error_description", "Unknown error occurred"
                    ),
                },
                status_code=400,
            )

        # Get user info
        access_token = result.get("access_token")
        if not access_token:
            logger.error("No access token in result")
            return JSONResponse(
                content={
                    "error": "Authentication Error",
                    "message": "Failed to acquire access token from Microsoft.",
                    "details": "Please try logging in again.",
                },
                status_code=500,
            )

        logger.info(f"Successfully acquired access token (length: {len(access_token)})")

        id_token_claims = result.get("id_token_claims", {})
        user_display_name = id_token_claims.get("name", "Unknown User")
        user_email = id_token_claims.get("preferred_username", "<EMAIL>")

        # Ensure basic authentication is also set
        if "basic_user" not in session:
            logger.info("Setting basic user authentication in session")
            session["basic_user"] = {
                "username": "microsoft_user",
                "authenticated": True,
            }

        # Store token cache in session
        logger.info("Storing token in session")
        session["ms_token_cache"] = {
            "access_token": access_token,
            "expires_in": result.get("expires_in", 3600),
            "ext_expires_in": result.get("ext_expires_in", 3600),
        }

        # Store basic user info
        session["ms_user"] = {
            "name": user_display_name,
            "email": user_email,
        }

        # Clean up the state
        if "ms_auth_state" in session:
            del session["ms_auth_state"]

        logger.info(f"Microsoft authentication successful for user: {user_email}")

        # Create a full URL for the redirect
        redirect_to = request.url_for("list_sharepoint_sites")
        logger.info(f"Redirecting to: {redirect_to}")

        return RedirectResponse(url=str(redirect_to), status_code=303)

    except Exception as e:
        logger.error(f"Error in Microsoft auth callback: {e}", exc_info=True)
        return JSONResponse(
            content={
                "error": "Authentication Error",
                "message": "Microsoft authentication failed.",
                "details": str(e),
            },
            status_code=500,
        )


@app.get("/sharepoint/sites", name="list_sharepoint_sites")
async def list_sharepoint_sites_view(request: Request, user=BasicUserDep):
    """Render the SharePoint sites view for the authenticated user."""
    try:
        logger.info("Starting list_sharepoint_sites_view endpoint")

        # First verify SharePoint client is initialized
        if not sharepoint_client:
            logger.error("SharePoint client is not initialized.")
            return templates.TemplateResponse(
                "error.html",
                {
                    "request": request,
                    "error_title": "SharePoint Not Configured",
                    "error_message": "SharePoint integration is not configured or failed to initialize.",
                    "error_details": "Check your server configuration and environment variables.",
                },
                status_code=500,
            )

        # Check for Microsoft authentication and force redirect if not present
        session = request.session
        if "ms_token_cache" not in session or "ms_user" not in session:
            logger.info(
                "No Microsoft authentication found, redirecting to Microsoft login"
            )
            redirect_url = request.url_for("login_microsoft")
            return RedirectResponse(url=str(redirect_url), status_code=302)

        # Get the access token
        cached_token = session.get("ms_token_cache", {})
        access_token = cached_token.get("access_token")

        if not access_token:
            logger.info("No valid access token found, redirecting to Microsoft login")
            # Clear any invalid session data
            if "ms_token_cache" in session:
                del session["ms_token_cache"]
            if "ms_user" in session:
                del session["ms_user"]
            redirect_url = request.url_for("login_microsoft")
            return RedirectResponse(url=str(redirect_url), status_code=302)

        try:
            # List SharePoint sites
            logger.info("Calling SharePoint client to list sites")
            sites = await sharepoint_client.list_sites(access_token)

            # Log the results for debugging
            site_count = len(sites) if sites else 0
            logger.info(f"Found {site_count} SharePoint sites")

            # Store the first site ID in the session (assuming it's the one we want)
            if sites and len(sites) > 0:
                session["current_site_id"] = sites[0].get("id", "")
                logger.info(f"Stored site ID in session: {session['current_site_id']}")

            return templates.TemplateResponse(
                "sharepoint_sites.html", {"request": request, "sites": sites}
            )
        except Exception as e:
            error_message = str(e).lower()
            # Check for any token-related errors
            if any(
                keyword in error_message
                for keyword in ["token", "401", "unauthorized", "expired"]
            ):
                logger.info("Token expired or invalid, redirecting to Microsoft login")
                # Clear the expired token
                if "ms_token_cache" in session:
                    del session["ms_token_cache"]
                if "ms_user" in session:
                    del session["ms_user"]
                redirect_url = request.url_for("login_microsoft")
                return RedirectResponse(url=str(redirect_url), status_code=302)
            raise  # Re-raise other exceptions

    except Exception as e:
        logger.error(f"Error in list_sharepoint_sites_view: {e}", exc_info=True)

        # Check if the error is likely due to token issues
        error_message = str(e).lower()
        if any(
            keyword in error_message
            for keyword in ["token", "401", "unauthorized", "expired"]
        ):
            logger.info("Token error detected, redirecting to Microsoft login")
            # Clear any invalid session data
            session = request.session
            if "ms_token_cache" in session:
                del session["ms_token_cache"]
            if "ms_user" in session:
                del session["ms_user"]
            redirect_url = request.url_for("login_microsoft")
            return RedirectResponse(url=str(redirect_url), status_code=302)

        # Handle other errors
        error_title = "SharePoint Error"
        error_message = str(e)
        status_code = 500

        if "permission denied" in error_message.lower():
            status_code = 403
            error_title = "Permission Error"
            error_message = (
                "You don't have sufficient permissions to access SharePoint content."
            )
        elif "not found" in error_message.lower():
            status_code = 404
            error_title = "Site Not Found"
            error_message = "The SharePoint site was not found or may have been moved."

        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_title": error_title,
                "error_message": error_message,
                "error_details": f"Exception type: {type(e).__name__}, Message: {str(e)}",
            },
            status_code=status_code,
        )


@app.get("/sharepoint/drives/{site_id}", name="list_sharepoint_drives")
async def list_sharepoint_drives(
    request: Request, site_id: str, user=BasicUserDep, ms_token: str = MsTokenDep
):
    """List document libraries (drives) within a SharePoint site."""
    if not sharepoint_client:
        logger.error("SharePoint client is not initialized.")
        raise HTTPException(
            status_code=500,
            detail="SharePoint integration is not configured or failed to initialize.",
        )

    try:
        logger.info(f"Listing drives for site ID: {site_id}")
        logger.info(f"Token length: {len(ms_token) if ms_token else 0} characters")

        # Special case for OneDrive for Business sites - directly return the OneDrive
        if "/personal/" in site_id or "_personal_" in site_id:
            logger.info(
                "Personal OneDrive site detected. Attempting direct OneDrive approach."
            )
            try:
                async with aiohttp.ClientSession() as session:
                    # Get the user's OneDrive directly
                    me_drive_url = "https://graph.microsoft.com/v1.0/me/drive"
                    logger.info(f"Getting user's OneDrive: {me_drive_url}")

                    async with session.get(
                        me_drive_url, headers={"Authorization": f"Bearer {ms_token}"}
                    ) as response:
                        if response.status == 200:
                            drive_data = await response.json()
                            logger.info(f"Successfully retrieved OneDrive")

                            # Create a list with just this drive
                            drives = [drive_data]
                            return templates.TemplateResponse(
                                "sharepoint_drives.html",
                                {
                                    "request": request,
                                    "drives": drives,
                                    "site_id": site_id,
                                },
                            )
                        else:
                            error_text = await response.text()
                            logger.error(
                                f"Failed to get OneDrive: {response.status} - {error_text}"
                            )
            except Exception as e:
                logger.error(f"Error in OneDrive approach: {e}", exc_info=True)
                # Fall back to standard approach

        # Standard approach for regular SharePoint sites
        drives = await sharepoint_client.list_drives(site_id=site_id, token=ms_token)
        logger.info(f"Found {len(drives)} drives for site {site_id}")

        return templates.TemplateResponse(
            "sharepoint_drives.html",
            {"request": request, "drives": drives, "site_id": site_id},
        )
    except Exception as e:
        logger.error(f"Error listing drives for site {site_id}: {e}", exc_info=True)

        # Check if the error is likely due to token issues (e.g., Graph API errors)
        error_detail = f"Failed to list document libraries: {e}"
        status_code = 500

        if "401" in str(e) or "Unauthorized" in str(e):
            status_code = 401
            error_detail = "Failed to list document libraries: Authentication failed or token expired. Please try logging out and back in via Microsoft."

        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_title": "SharePoint Error",
                "error_message": error_detail,
                "error_details": f"Exception type: {type(e).__name__}, Message: {str(e)}",
            },
            status_code=status_code,
        )


@app.get("/sharepoint/files/{drive_id}", name="list_sharepoint_files")
async def list_sharepoint_files(
    request: Request,
    drive_id: str,
    folder_path: str = "",
    user=BasicUserDep,
    ms_token: str = MsTokenDep,
):
    """List files and folders in a SharePoint drive or folder."""
    if not sharepoint_client:
        logger.error("SharePoint client is not initialized.")
        raise HTTPException(
            status_code=500,
            detail="SharePoint integration is not configured or failed to initialize.",
        )

    try:
        logger.info(
            f"Listing files for drive ID: {drive_id}, folder path: {folder_path}"
        )
        logger.info(f"Token length: {len(ms_token) if ms_token else 0} characters")

        # Get the files from SharePoint
        files = await sharepoint_client.list_files(
            drive_id=drive_id, token=ms_token, folder_path=folder_path
        )
        logger.info(f"Found {len(files)} files/folders")

        # Get the site ID from the session or a default value
        site_id = request.session.get("current_site_id", "")

        return templates.TemplateResponse(
            "sharepoint_files.html",
            {
                "request": request,
                "files": files,
                "drive_id": drive_id,
                "current_path": folder_path,
                "site_id": site_id,
            },
        )
    except Exception as e:
        logger.error(f"Error listing files for drive {drive_id}: {e}", exc_info=True)

        # Check if the error is likely due to token issues
        error_detail = f"Failed to list files: {e}"
        status_code = 500

        if "401" in str(e) or "Unauthorized" in str(e):
            status_code = 401
            error_detail = "Failed to list files: Authentication failed or token expired. Please try logging out and back in via Microsoft."

        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_title": "SharePoint Error",
                "error_message": error_detail,
                "error_details": f"Exception type: {type(e).__name__}, Message: {str(e)}",
            },
            status_code=status_code,
        )


async def import_sharepoint_item(request: Request, drive_id: str, item_id: str) -> dict:
    """Import a file or folder from SharePoint with enhanced batch processing and error handling."""
    temp_dir = None
    try:
        temp_dir = Path(settings.TEMP_DIR)
        temp_dir.mkdir(parents=True, exist_ok=True)
        access_token = await get_microsoft_access_token(request)
        item = await app.state.sharepoint_client.get_drive_item(
            drive_id, item_id, token=access_token
        )
        if not item:
            raise ValueError("Item not found")
        progress = {
            "total_files": 0,
            "processed_files": 0,
            "failed_files": 0,
            "skipped_files": [],
            "errors": [],
        }

        async def process_single_file(file_item, parent_path=""):
            file_path_for_error = file_item.get("name", "Unknown File")
            try:
                file_name = file_item.get("name", "")
                file_path = parent_path + "/" + file_name if parent_path else file_name
                file_path_for_error = file_path
                file_size = file_item.get("size", 0)
                file_ext = Path(file_name).suffix.lower()

                # Check file size limitation
                if file_size > 100 * 1024 * 1024:
                    progress["skipped_files"].append(f"{file_path} (too large)")
                    return

                # Check supported file types
                if file_ext not in [
                    ".txt",
                    ".pdf",
                    ".doc",
                    ".docx",
                    ".rtf",
                    ".csv",
                    ".xlsx",
                    ".xls",
                    ".png",
                    ".jpg",
                    ".jpeg",
                    ".gif",
                    ".bmp",
                ]:
                    progress["skipped_files"].append(f"{file_path} (unsupported type)")
                    return

                # Download the file
                temp_file_path = temp_dir / file_name
                await app.state.sharepoint_client.download_file(
                    drive_id, file_item["id"], str(temp_file_path), access_token
                )

                # Special handling for specific problematic file
                if "DDB_BrandGuidelines-2025.pdf" in str(temp_file_path):
                    logger.warning(f"Detected problematic PDF file: {temp_file_path}")
                    try:
                        # Create a stub entry for the problematic PDF
                        metadata = {
                            "sharepoint_id": file_item["id"],
                            "created_datetime": file_item.get("createdDateTime"),
                            "last_modified_datetime": file_item.get(
                                "lastModifiedDateTime"
                            ),
                            "created_by": file_item.get("createdBy", {})
                            .get("user", {})
                            .get("displayName"),
                            "web_url": file_item.get("webUrl"),
                            "parent_path": parent_path,
                            "is_stub": True,
                        }

                        # Create a TextNode directly with a stub message
                        stub_text = f"DDB Brand Guidelines (2025) - This document contains design and branding guidelines for DDB. Please access the original document on SharePoint for complete details."
                        node = TextNode(
                            text=stub_text,
                            metadata={**metadata, "file_name": file_name},
                        )
                        app.state.index.insert_nodes([node])
                        await persist_index()
                        progress["processed_files"] += 1
                        logger.info(f"Successfully created stub entry for {file_name}")
                        temp_file_path.unlink()
                        return
                    except Exception as e:
                        logger.error(f"Error creating stub for {file_path}: {str(e)}")
                        progress["failed_files"] += 1
                        progress["errors"].append(f"Error with {file_path}: {str(e)}")
                        return

                # Standard processing for other files
                metadata = {
                    "sharepoint_id": file_item["id"],
                    "created_datetime": file_item.get("createdDateTime"),
                    "last_modified_datetime": file_item.get("lastModifiedDateTime"),
                    "created_by": file_item.get("createdBy", {})
                    .get("user", {})
                    .get("displayName"),
                    "web_url": file_item.get("webUrl"),
                    "parent_path": parent_path,
                }
                await add_document_to_index(str(temp_file_path), metadata)
                progress["processed_files"] += 1
                temp_file_path.unlink()
            except Exception as e:
                error_msg = f"Error processing {file_path_for_error}: {str(e)}"
                logger.error(error_msg)
                progress["failed_files"] += 1
                progress["errors"].append(error_msg)

        async def process_folder(folder_item, parent_path=""):
            folder_name_for_error = folder_item.get("name", "Unknown Folder")
            try:
                folder_name = folder_item.get("name", "")
                folder_name_for_error = (
                    f"{parent_path}/{folder_name}" if parent_path else folder_name
                )
                new_parent = (
                    parent_path + "/" + folder_name if parent_path else folder_name
                )
                logger.info(f"Processing folder: {new_parent}")
                async for (
                    child
                ) in app.state.sharepoint_client.list_folder_contents_recursive(
                    drive_id, folder_item["id"], token=access_token
                ):
                    logger.debug(
                        f"Processing child item: Name={child.get('name')}, Keys={list(child.keys())}, IsFolder={child.get('folder')}, IsFile={child.get('file')}"
                    )
                    if "folder" in child:
                        await process_folder(child, new_parent)
                    else:
                        progress["total_files"] += 1
                        await process_single_file(child, new_parent)
            except Exception as e:
                error_msg = f"Error processing folder {folder_name_for_error}: {str(e)}"
                logger.error(error_msg, exc_info=True)
                progress["errors"].append(error_msg)

        if "folder" in item:
            await process_folder(item)
        else:
            progress["total_files"] = 1
            await process_single_file(item)

        return {
            "status": "completed",
            "total_files": progress["total_files"],
            "processed_files": progress["processed_files"],
            "failed_files": progress["failed_files"],
            "skipped_files": progress["skipped_files"],
            "errors": progress["errors"],
        }

    except Exception as e:
        logger.error(f"Import failed: {str(e)}")
        return {"status": "failed", "error": str(e)}
    finally:
        if temp_dir and temp_dir.exists():
            shutil.rmtree(temp_dir)


@app.get(
    "/sharepoint/import/{drive_id}/{item_id}/{item_name}",
    name="import_sharepoint_item_route",
)
async def import_sharepoint_item_route(
    request: Request,
    drive_id: str,
    item_id: str,
    item_name: str,
    user=BasicUserDep,
    ms_token: str = MsTokenDep,
):
    """
    Import a file or folder from SharePoint and render appropriate response.
    """
    try:
        # Call the import function, passing the request object
        result = await import_sharepoint_item(request, drive_id, item_id)

        if result["status"] == "completed":
            # Build success message
            message = f"Successfully processed {result['processed_files']} of {result['total_files']} files from '{item_name}'."
            details = []

            if result["skipped_files"]:
                details.append(f"Skipped files: {', '.join(result['skipped_files'])}")

            if result["failed_files"] > 0:
                details.append(f"Failed to process {result['failed_files']} files:")
                details.extend([f"  - {error}" for error in result["errors"]])

            return templates.TemplateResponse(
                "success.html",
                {
                    "request": request,
                    "message": message,
                    "details": "\n".join(details) if details else None,
                },
            )
        else:
            # Handle failed import
            return templates.TemplateResponse(
                "error.html",
                {
                    "request": request,
                    "error_title": "Import Failed",
                    "error_message": f"Failed to import '{item_name}'",
                    "error_details": result.get("error", "Unknown error occurred"),
                },
                status_code=500,
            )

    except Exception as e:
        logger.error(f"Error in import route: {str(e)}")
        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_title": "Import Error",
                "error_message": f"An unexpected error occurred while importing '{item_name}'",
                "error_details": str(e),
            },
            status_code=500,
        )


# --- RAG Query Endpoints ---
@app.get("/query")
async def query_endpoint(
    request: Request,
    query: str = Query(..., min_length=1, max_length=500),
    user=BasicUserDep,
):
    """Query the RAG system with a natural language question."""
    try:
        # Check if query engine is available and initialize if needed
        if not hasattr(app.state, "query_engine") or not app.state.query_engine:
            logger.warning("Query engine not available - attempting to initialize")

            # Ensure index is loaded
            if not hasattr(app.state, "index") or not app.state.index:
                try:
                    logger.info("Index not found, attempting to load...")
                    app.state.index = await load_index()
                    logger.info("Index loaded successfully")
                except Exception as e:
                    logger.error(f"Failed to load index: {e}")
                    return JSONResponse(
                        status_code=500,
                        content={
                            "error": "Failed to load vector index",
                            "details": str(e),
                        },
                    )

            # Create query engine from index
            try:
                app.state.query_engine = get_query_engine(
                    app.state.index, app.state.reranker
                )
                logger.info("Query engine successfully initialized on-demand")
            except Exception as model_error:
                logger.error(
                    f"Error creating query engine with current model: {model_error}"
                )

                # Try with a more reliable fallback model
                try:
                    logger.info(
                        "Attempting to create query engine with fallback model..."
                    )
                    fallback_llm = LlamaOpenAI(
                        model="gpt-3.5-turbo",  # Fallback to a more widely available model
                        temperature=0.7,
                        max_tokens=1500,
                    )

                    # Update the global settings
                    LlamaSettings.llm = fallback_llm

                    # Try again with fallback model
                    app.state.query_engine = get_query_engine(
                        app.state.index, app.state.reranker
                    )
                    logger.info("Successfully created query engine with fallback model")
                except Exception as fallback_error:
                    logger.error(f"Fallback model also failed: {fallback_error}")
                    return JSONResponse(
                        status_code=500,
                        content={
                            "error": "Query engine initialization failed",
                            "details": f"Original error: {model_error}. Fallback error: {fallback_error}",
                        },
                    )

        # Verify query engine is actually initialized
        if not app.state.query_engine:
            logger.error(
                "Query engine still not available after initialization attempts"
            )
            return JSONResponse(
                status_code=500,
                content={
                    "error": "Unable to initialize query engine",
                    "details": "Please contact the administrator",
                },
            )

        # Process query and get result
        logger.info(f"Processing query: {query}")

        # Enhance the query to request more detailed responses
        enhanced_query = f"""Please provide a detailed and comprehensive answer to the following question. Include relevant examples, explanations, and context where appropriate. Break down complex concepts into understandable parts.

Question: {query}"""

        try:
            response = await asyncio.to_thread(
                app.state.query_engine.query, enhanced_query
            )
            logger.info(f"Query successful. Response type: {type(response)}")
        except Exception as query_error:
            logger.error(f"Error executing query: {query_error}")
            return JSONResponse(
                status_code=500,
                content={
                    "error": "Failed to execute query",
                    "details": str(query_error),
                },
            )

        # Format the response for better presentation
        formatted_response = format_response(str(response))

        # Extract source nodes for citation
        source_nodes = []
        if hasattr(response, "source_nodes") and response.source_nodes:
            logger.info(f"Found {len(response.source_nodes)} source nodes.")
            for i, node in enumerate(response.source_nodes):
                metadata = node.node.metadata
                source_text = node.node.get_content(metadata_mode=MetadataMode.NONE)

                # Include only relevant metadata
                source_info = {
                    "id": i,
                    "score": float(node.score) if hasattr(node, "score") else 0,
                    "text": source_text[:250] + "..."
                    if len(source_text) > 250
                    else source_text,
                }

                # Add file metadata if available
                if "file_name" in metadata:
                    source_info["file_name"] = metadata["file_name"]
                if "file_type" in metadata:
                    source_info["file_type"] = metadata["file_type"]
                if "created_at" in metadata:
                    source_info["created_at"] = metadata["created_at"]

                source_nodes.append(source_info)
        else:
            logger.info("No source nodes found in the response.")

        # Generate follow-up questions if output is from LLM
        follow_up_questions = []
        if hasattr(app.state, "llm") and app.state.llm:
            try:
                # Generate more contextual follow-up questions
                prompt = f"""Based on the original question: "{query}" and the detailed answer provided: "{str(response)}", 
                suggest 3 insightful follow-up questions that would help explore this topic further or clarify important aspects.
                Consider:
                - Deeper aspects of the topic that weren't fully covered
                - Practical applications or implications
                - Related concepts that might be interesting to explore
                Format as a numbered list without explanations. Keep questions clear and directly related."""

                followup_resp = await app.state.llm.acomplete(prompt)

                # Parse the response to extract questions
                followup_text = str(followup_resp)
                for line in followup_text.strip().split("\n"):
                    # Match numbered list items like "1. Question" or "1) Question"
                    import re

                    match = re.match(r"^\s*\d+[\.\)]\s*(.+)$", line)
                    if match:
                        question = match.group(1).strip()
                        if question and len(question) > 10:
                            follow_up_questions.append(question)

                # Limit to 3 questions
                follow_up_questions = follow_up_questions[:3]
                logger.info(
                    f"Generated {len(follow_up_questions)} follow-up questions: {follow_up_questions}"
                )

            except Exception as e:
                logger.error(f"Error generating follow-up questions: {e}")
                # Continue without follow-up questions

        logger.info(f"Sending response for query: {query}")
        return JSONResponse(
            content={
                "response": formatted_response,
                "source_nodes": source_nodes,
                "follow_up_questions": follow_up_questions,
                "query": query,
            }
        )

    except Exception as e:
        logger.error(f"Error processing query '{query}': {e}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={
                "error": f"An unexpected error occurred while processing your query.",
                "details": str(e),
            },
        )


@app.get("/health")
async def health_check():
    """Endpoint to check the health of the application."""
    try:
        # Basic health check
        health_status = {
            "status": "healthy",
            "time": datetime.now().isoformat(),
        }

        # Check if query engine is initialized
        if hasattr(app.state, "query_engine"):
            health_status["query_engine"] = "initialized"
        else:
            health_status["query_engine"] = "not initialized"

        # Check documents in index if available
        if hasattr(app.state, "index") and hasattr(app.state.index, "docstore"):
            doc_count = len(app.state.index.docstore.docs)
            health_status["index_document_count"] = doc_count
            logger.info(f"Index health check: {doc_count} documents found")
        else:
            health_status["index_document_count"] = 0

        # Check SharePoint client status
        if sharepoint_client:
            health_status["sharepoint_client"] = "initialized"
        else:
            health_status["sharepoint_client"] = "not initialized or disabled"

        return JSONResponse(content=health_status)

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=500,
            content={"status": "unhealthy", "error": str(e)},
        )


# --- Template Routes ---
@app.get("/")
async def root(request: Request):
    """Render the index template."""
    return templates.TemplateResponse("index.html", {"request": request})


@app.get("/documents")
async def documents_page(request: Request, user=BasicUserDep):
    """Render the document management page, showing unique indexed files."""
    unique_files = set()  # Use a set to store unique filenames
    logger.info("Accessing /documents page...")  # Log entry
    try:
        # Check if index and docstore exist
        if (
            hasattr(app.state, "index")
            and app.state.index
            and hasattr(app.state.index, "docstore")
        ):
            # Access the documents dictionary directly (common pattern)
            if hasattr(app.state.index.docstore, "docs"):
                all_docs = app.state.index.docstore.docs
                logger.info(f"Found {len(all_docs)} entries in docstore.")
                # Extract unique filenames from metadata
                doc_count_with_filename = 0
                for doc_id, doc_info in all_docs.items():
                    if (
                        hasattr(doc_info, "metadata")
                        and "file_name" in doc_info.metadata
                    ):
                        doc_count_with_filename += 1
                        unique_files.add(doc_info.metadata["file_name"])
                    else:
                        # Log if metadata or file_name is missing
                        metadata_exists = hasattr(doc_info, "metadata")
                        logger.warning(
                            f"Document ID {doc_id} missing filename. Metadata exists: {metadata_exists}"
                        )
                        if metadata_exists:
                            logger.warning(
                                f"Metadata keys for doc {doc_id}: {list(doc_info.metadata.keys())}"
                            )

                logger.info(
                    f"Found {doc_count_with_filename} docs with filename metadata. Unique files: {len(unique_files)}"
                )
            else:
                logger.warning(
                    "Index docstore exists, but could not find 'docs' attribute."
                )
        else:
            index_exists = hasattr(app.state, "index") and app.state.index
            docstore_exists = index_exists and hasattr(app.state.index, "docstore")
            logger.warning(
                f"Index or docstore not found in app state. Index exists: {index_exists}, Docstore exists: {docstore_exists}"
            )

    except Exception as e:
        logger.error(
            f"Error retrieving indexed documents for /documents page: {e}",
            exc_info=True,
        )
        # Continue without document list if error occurs

    # Always render the template, passing the list of unique filenames
    logger.info(f"Rendering documents page with files: {sorted(list(unique_files))}")
    return templates.TemplateResponse(
        "documents.html",
        {
            "request": request,
            "indexed_files": sorted(list(unique_files)),
        },  # Pass sorted list
    )


async def process_image_file(file_path: Path) -> str:
    """
    Process an image file using OCR to extract text.
    Args:
        file_path: Path to the image file
    Returns:
        Extracted text from the image
    """
    try:
        # Open the image using PIL
        with Image.open(file_path) as img:
            # Convert image to RGB if necessary
            if img.mode not in ("L", "RGB"):
                img = img.convert("RGB")

            # Extract text using pytesseract
            text = pytesseract.image_to_string(img)

            # Add image metadata
            metadata = f"""
            Image Information:
            Filename: {file_path.name}
            Size: {img.size}
            Mode: {img.mode}
            Format: {img.format}
            
            Extracted Text:
            {text}
            """
            return metadata
    except Exception as e:
        logger.error(f"Error processing image {file_path}: {e}")
        return f"Error processing image {file_path.name}: {str(e)}"


async def process_pdf_with_fallback(file_path: Path) -> str:
    """
    Process PDF using multiple fallback methods.
    First tries SimpleDirectoryReader, then PyPDF2 if that fails.

    Args:
        file_path: Path to the PDF file

    Returns:
        Extracted text from the PDF
    """
    logger.info(f"Processing PDF with fallback methods: {file_path}")

    # Method 1: Try with SimpleDirectoryReader (LlamaIndex's default)
    try:
        logger.info(f"Trying SimpleDirectoryReader for: {file_path}")
        loader = SimpleDirectoryReader(input_files=[str(file_path)])
        documents = loader.load_data()

        if documents and documents[0].text and len(documents[0].text.strip()) > 0:
            logger.info(
                f"Successfully extracted {len(documents[0].text)} characters with SimpleDirectoryReader"
            )
            return documents[0].text
        else:
            logger.warning(
                f"SimpleDirectoryReader returned empty content for {file_path}"
            )
    except Exception as e:
        logger.warning(f"SimpleDirectoryReader failed for {file_path}: {str(e)}")

    # Method 2: Try with PyPDF2 if available
    if PYPDF2_AVAILABLE:
        try:
            logger.info(f"Trying PyPDF2 for: {file_path}")
            text = ""
            with open(file_path, "rb") as file:
                reader = PyPDF2.PdfReader(file)
                num_pages = len(reader.pages)
                logger.info(f"PDF has {num_pages} pages")

                for page_num in range(num_pages):
                    page = reader.pages[page_num]
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n\n"

            if text and len(text.strip()) > 0:
                logger.info(
                    f"Successfully extracted {len(text)} characters with PyPDF2"
                )
                return text
            else:
                logger.warning(f"PyPDF2 returned empty content for {file_path}")
        except Exception as e:
            logger.warning(f"PyPDF2 failed for {file_path}: {str(e)}")
    else:
        logger.warning("PyPDF2 not available, skipping this method")

    # Method 3: Return a placeholder text when all extraction methods fail
    logger.error(
        f"All extraction methods failed for PDF {file_path}, using placeholder text"
    )
    filename = file_path.name
    return f"This is a placeholder for the content of {filename}. The text extraction methods failed to extract content from this PDF. Please refer to the original document for the actual content."


async def add_document_to_index(file_path: str, metadata: dict = None) -> None:
    """Add a document to the vector index with enhanced file type support and metadata."""
    try:
        file_path = Path(file_path)
        logger.info(f"Processing file: {file_path}")
        file_ext = file_path.suffix.lower()
        file_size = file_path.stat().st_size
        MAX_FILE_SIZE = 100 * 1024 * 1024
        if file_size > MAX_FILE_SIZE:
            raise ValueError(
                f"File size ({file_size} bytes) exceeds maximum allowed size of {MAX_FILE_SIZE} bytes"
            )

        content = ""
        if file_ext in [".png", ".jpg", ".jpeg", ".gif", ".bmp"]:
            content = await process_image_file(file_path)
        elif file_ext == ".pdf":
            # Use specialized PDF processor with fallbacks for PDF files
            content = await process_pdf_with_fallback(file_path)
            logger.info(f"PDF processing complete: extracted {len(content)} characters")
        elif file_ext in [".doc", ".docx", ".txt", ".rtf"]:
            try:
                logger.info(f"Loading document with SimpleDirectoryReader: {file_path}")
                loader = SimpleDirectoryReader(input_files=[str(file_path)])
                documents = loader.load_data()
                logger.info(f"Documents loaded: {len(documents) if documents else 0}")

                if not documents:
                    error_msg = f"No documents returned by loader for {file_path}"
                    logger.error(error_msg)
                    raise ValueError(error_msg)

                logger.info(
                    f"First document text length: {len(documents[0].text) if documents[0].text else 0}"
                )

                if (
                    not documents[0].text
                    or not isinstance(documents[0].text, str)
                    or not documents[0].text.strip()
                ):
                    error_msg = f"Invalid or empty text content from {file_path}"
                    logger.error(error_msg)
                    raise ValueError(error_msg)

                content = documents[0].text
                logger.info(
                    f"Successfully extracted {len(content)} characters from {file_path}"
                )

            except Exception as e:
                logger.error(f"Error processing document {file_path}: {str(e)}")
                if "NoneType" in str(e):
                    logger.error(
                        "Document text is None - this may indicate a corrupted PDF or unsupported format"
                    )
                raise
        elif file_ext in [".xlsx", ".xls", ".csv"]:
            try:
                import pandas as pd

                if file_ext == ".csv":
                    df = pd.read_csv(file_path)
                else:
                    df = pd.read_excel(file_path)
                content = f"Spreadsheet Contents:\n\nColumns: {', '.join(df.columns)}\n\nData Summary:\n{df.head().to_string()}"
            except Exception as e:
                logger.error(f"Error processing spreadsheet {file_path}: {e}")
                raise
        else:
            try:
                loader = SimpleDirectoryReader(input_files=[str(file_path)])
                documents = loader.load_data()
                if not documents or not documents[0].text:
                    error_msg = f"No content could be extracted from {file_path}"
                    logger.error(error_msg)
                    raise ValueError(error_msg)
                content = documents[0].text
            except Exception as e:
                logger.error(f"Error processing unknown file type {file_ext}: {e}")
                raise

        # Defensive check: Only proceed if content is a non-empty string
        if not content or not isinstance(content, str) or not content.strip():
            error_msg = (
                f"No valid content extracted from {file_path} (None or empty string)"
            )
            logger.error(error_msg)
            raise ValueError(error_msg)

        # Prepare metadata (only after content is validated)
        doc_metadata = {
            "file_name": file_path.name,
            "file_type": file_ext,
            "file_size": file_size,
            "import_timestamp": datetime.now().isoformat(),
            "content_length": len(content),
        }
        if metadata:
            doc_metadata.update(metadata)

        nodes = []
        try:
            node = TextNode(
                text=content, metadata={**doc_metadata, "file_name": file_path.name}
            )
            nodes.append(node)
        except Exception as node_error:
            logger.error(f"Error creating TextNode for {file_path}: {node_error}")
            raise

        if not nodes:
            error_msg = f"No text nodes created for file {file_path}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        inserted_nodes = app.state.index.insert_nodes(nodes)
        logger.info(
            f"Inserted {len(inserted_nodes)} nodes into index for {file_path.name}"
        )

        await persist_index()

        # Reload index and query engine after persisting
        try:
            app.state.index = await load_index()
            app.state.query_engine = get_query_engine(
                app.state.index, app.state.reranker
            )
            logger.info(
                f"Reloaded index and query engine after indexing {file_path.name}"
            )
        except Exception as e:
            logger.error(f"Error reloading index/query engine after indexing: {e}")
            raise

        logger.info(f"Successfully indexed {file_path.name} with metadata")

    except Exception as e:
        logger.error(f"Error processing file {file_path.name}: {e}")
        raise


async def persist_index():
    """Persist the index to storage."""
    try:
        if hasattr(app.state, "index") and app.state.index:
            logger.info("Persisting index to storage...")
            app.state.index.storage_context.persist(
                persist_dir=str(settings.STORAGE_DIR)
            )
            logger.info("Index persisted successfully")
        else:
            logger.warning("Index not found in application state. Cannot persist.")
    except Exception as e:
        logger.error(f"Error persisting index: {e}", exc_info=True)


@app.get("/api/documents")
async def get_documents(request: Request, user=BasicUserDep):
    """Get the list of unique indexed documents."""
    logger.info("Attempting to access /api/documents...")
    if not user or not user.get("authenticated"):
        # This check should be handled by BasicUserDep, but logging helps
        logger.warning("/api/documents accessed without authenticated user in session.")
        raise HTTPException(status_code=401, detail="Not authenticated")

    logger.info(f"/api/documents accessed by user: {user.get('username')}")
    unique_files = set()
    try:
        if (
            hasattr(app.state, "index")
            and app.state.index
            and hasattr(app.state.index, "docstore")
            and hasattr(app.state.index.docstore, "docs")
        ):
            all_docs = app.state.index.docstore.docs
            for doc_id, doc_info in all_docs.items():
                if hasattr(doc_info, "metadata") and "file_name" in doc_info.metadata:
                    unique_files.add(doc_info.metadata["file_name"])
            logger.info(f"/api/documents found {len(unique_files)} unique files.")
        else:
            logger.warning("/api/documents: Index or docstore not ready.")

    except Exception as e:
        logger.error(
            f"Error retrieving documents for /api/documents: {e}", exc_info=True
        )
        # Return empty list on error, but log it

    return {"documents": sorted(list(unique_files))}


async def load_index():
    """Load the vector index from storage or create a new one if it doesn't exist."""
    logger.info("Attempting to load index from local storage...")
    storage_dir = settings.STORAGE_DIR

    try:
        if storage_dir.exists() and any(settings.STORAGE_DIR.iterdir()):
            # Attempt to load existing index
            try:
                storage_context = StorageContext.from_defaults(
                    persist_dir=str(storage_dir)
                )
                index = load_index_from_storage(storage_context)
                logger.info("Index loaded successfully from local storage.")
                return index
            except Exception as e:
                logger.error(f"Error loading existing index: {e}")
                logger.warning("Will attempt to create a new index")
        else:
            logger.warning("No existing index found in storage directory")

        # Index doesn't exist or couldn't be loaded - create a new one
        logger.info("Creating new empty index...")
        llm = LlamaOpenAI(
            model=OPENAI_MODEL,
            temperature=0.7,
            max_tokens=2000,
        )
        embed_model = OpenAIEmbedding(model="text-embedding-3-small")

        LlamaSettings.llm = llm
        LlamaSettings.embed_model = embed_model

        # Create an empty vector store index
        empty_index = VectorStoreIndex([])

        # Store the new index
        storage_context = empty_index.storage_context
        storage_context.persist(persist_dir=str(storage_dir))
        logger.info("Successfully created and persisted new empty index")

        return empty_index
    except Exception as e:
        logger.error(f"Critical error in load_index: {str(e)}", exc_info=True)
        # As a last resort, create an in-memory index that won't be persisted
        logger.warning("Creating transient in-memory index as fallback")
        return VectorStoreIndex([])
