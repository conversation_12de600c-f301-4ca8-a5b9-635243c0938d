import os
import logging
import sys
from dotenv import load_dotenv
from llama_index.core import (
    VectorStoreIndex,
    SimpleDirectoryReader,
    StorageContext,
    <PERSON><PERSON><PERSON>,
    get_response_synthesizer,
)
from llama_index.core.node_parser import SentenceSplitter
from llama_index.core.retrievers import VectorIndexRetriever
from llama_index.core.query_engine import RetrieverQueryEngine
from llama_index.llms.openai import OpenAI
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.postprocessor.cohere_rerank import CohereRerank

# Load environment variables
load_dotenv()

# --- Configuration ---
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
COHERE_API_KEY = os.getenv("COHERE_API_KEY")

if not OPENAI_API_KEY:
    raise ValueError("OPENAI_API_KEY environment variable not set.")

# Configure logging
logging.basicConfig(stream=sys.stdout, level=logging.INFO)
logging.getLogger().addHandler(logging.StreamHandler(stream=sys.stdout))

# --- Model & Embedding Configuration ---
llm = OpenAI(model="gpt-4o", api_key=OPENAI_API_KEY)
embed_model = OpenAIEmbedding(model="text-embedding-3-small", api_key=OPENAI_API_KEY)

# --- Reranker Configuration ---
reranker = CohereRerank(api_key=COHERE_API_KEY, top_n=3)

# --- LlamaIndex Settings Configuration ---
Settings.llm = llm
Settings.embed_model = embed_model
Settings.node_parser = SentenceSplitter(chunk_size=512, chunk_overlap=20)


def setup_data_directory():
    """Create data directory and sample document if they don't exist."""
    if not os.path.exists("data"):
        os.makedirs("data")
    if not os.path.exists("data/sample_doc.txt"):
        with open("data/sample_doc.txt", "w") as f:
            f.write("The quick brown fox jumps over the lazy dog. \n")
            f.write(
                "Large Language Models (LLMs) are trained on vast amounts of text data. \n"
            )
            f.write(
                "Retrieval-Augmented Generation (RAG) combines retrieval with generation. \n"
            )
            f.write(
                "Reranking helps improve the relevance of retrieved documents before generation. \n"
            )
            f.write("The capital of France is Paris. \n")


def create_index():
    """Create and return the vector store index."""
    print("Loading data...")
    documents = SimpleDirectoryReader("data").load_data()
    print(f"Loaded {len(documents)} document(s).")

    print("Indexing documents...")
    index = VectorStoreIndex.from_documents(documents)
    print("Indexing complete.")
    return index


def setup_query_engine(index):
    """Set up and return the query engine with retriever and reranker."""
    retriever_top_k = 10
    retriever = VectorIndexRetriever(
        index=index,
        similarity_top_k=retriever_top_k,
    )
    print(f"Retriever configured to fetch top {retriever_top_k} results.")

    response_synthesizer = get_response_synthesizer(llm=llm)

    query_engine = RetrieverQueryEngine(
        retriever=retriever,
        response_synthesizer=response_synthesizer,
        node_postprocessors=[reranker],
    )
    print("Query engine created with retriever and reranker.")
    return query_engine


def main():
    """Main function to run the RAG application."""
    try:
        # Setup data directory
        setup_data_directory()

        # Create index
        index = create_index()

        # Setup query engine
        query_engine = setup_query_engine(index)

        # Interactive query loop
        print("\nEnter your questions (type 'quit' to exit):")
        while True:
            query = input("\nYour question: ").strip()
            if query.lower() == "quit":
                break

            if not query:
                continue

            print(f"\nQuerying: {query}")
            response = query_engine.query(query)
            print("\nResponse:")
            print(response)

    except Exception as e:
        print(f"An error occurred: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
