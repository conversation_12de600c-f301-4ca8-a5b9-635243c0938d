# DDB SharePoint RAG Application

This is a Retrieval-Augmented Generation (RAG) application designed to interact with documents stored in SharePoint.

## Features

*   **SharePoint Integration:** Connects to SharePoint to list sites, drives, and files. Imports documents from SharePoint for indexing.
*   **RAG System:** Uses LlamaIndex, OpenAI, and Cohere to provide answers to user queries based on the indexed documents.
*   **Web Interface:** Provides a simple chat interface built with FastAPI and Jinja2 templates.
*   **Authentication:** Includes basic authentication and Microsoft OAuth2 for secure access.

## Setup

1.  **Clone the repository:**
    ```bash
    git clone https://github.com/ericxyz86/DDBSharepoint.git
    cd DDBSharepoint
    ```
2.  **Create a virtual environment:**
    ```bash
    python -m venv venv
    source venv/bin/activate # On Windows use `venv\Scripts\activate`
    ```
3.  **Install dependencies:**
    ```bash
    pip install -r requirements.txt
    ```
4.  **Configure environment variables:**
    *   Create a `.env` file in the root directory.
    *   Add the following variables (replace with your actual credentials):
        ```env
        # Basic Auth
        USERNAME=your_basic_auth_username
        PASSWORD=your_basic_auth_password
        SESSION_SECRET_KEY=generate_a_strong_random_key

        # OpenAI
        OPENAI_API_KEY=your_openai_api_key

        # Cohere
        COHERE_API_KEY=your_cohere_api_key

        # Microsoft Graph / SharePoint (Optional - for SharePoint integration)
        USE_SHAREPOINT=True # Set to False to disable
        MS_CLIENT_ID=your_azure_app_client_id
        MS_CLIENT_SECRET=your_azure_app_client_secret
        MS_TENANT_ID=your_azure_tenant_id
        SHAREPOINT_SITE_NAME="your_sharepoint_site_name" # e.g., yourtenant.sharepoint.com
        SHAREPOINT_LIBRARY_NAME="Documents" # Usually "Documents"

        # Application Settings
        HOST=127.0.0.1
        PORT=8082
        # CORS_ORIGINS="http://localhost:8082,http://127.0.0.1:8082" # Comma-separated list
        ```
5.  **Run the application:**
    ```bash
    uvicorn app:app --host localhost --port 8082 --reload
    ```

## Usage

*   Access the application in your browser at `http://localhost:8082`.
*   Log in using the basic authentication credentials.
*   Use the chat interface to ask questions about your indexed documents.
*   Navigate to `/documents` to manage uploaded files.
*   If SharePoint is enabled, navigate to `/sharepoint/sites` to browse and import files (requires Microsoft login).

## Development

### Logging

Logs are written to both console and `app.log` file. The logging level can be adjusted in the code.

### Configuration

All configuration is handled through environment variables in `.env`. See `.env.example` for available options.

### Security Best Practices

- Never commit `.env` file to version control
- Regularly rotate API keys and secrets
- Monitor logs for suspicious activity
- Keep dependencies updated
- Use HTTPS in production
- Regular security audits of the codebase

## Deployment

The application is configured for deployment on Render.com with persistent storage for documents and vector indices.

### Environment Variables

Required environment variables for deployment:
- `OPENAI_API_KEY`
- `COHERE_API_KEY`
- `ADMIN_PASSWORD`
- `SESSION_SECRET_KEY`
- `GOOGLE_APPLICATION_CREDENTIALS` (path to service account key file)
- `GCS_BUCKET_NAME` (Google Cloud Storage bucket name)

Optional variables with defaults:
- `ADMIN_USERNAME` (default: "admin")
- `FILE_SIZE_LIMIT` (default: 100MB)
- `OPENAI_MODEL` (default: "gpt-4-turbo-preview")
- `CHUNK_SIZE` (default: 512)
- `CHUNK_OVERLAP` (default: 50)
- `SIMILARITY_TOP_K` (default: 20)
- `CORS_ORIGINS` (default: localhost and render.com)

## Project Structure

```
.
├── app.py              # Main FastAPI application
├── config.py           # Configuration and settings management
├── static/            # Static files (images, etc.)
├── storage/           # Vector store and index storage
├── templates/         # HTML templates
│   └── index.html    # Main application template
├── requirements.txt   # Python dependencies
└── README.md         # Project documentation
```

## Security Features

- Role-based access control
- Secure document handling
- API key protection
- Session management
- File size limits and type restrictions
- Environment-based configuration
- Verified document deletion
- Automatic index verification
- Secure storage integration

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

Proprietary - All rights reserved 