{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('list_sharepoint_sites') }}">Sites</a></li>
            <li class="breadcrumb-item active" aria-current="page">Document Libraries</li>
        </ol>
    </nav>

    <h2 class="mb-4">Select a Document Library</h2>
    
    <div class="list-group">
        {% for drive in drives %}
        <a href="{{ url_for('list_sharepoint_files', drive_id=drive.id) }}" class="list-group-item list-group-item-action">
            <div class="d-flex w-100 justify-content-between">
                <h5 class="mb-1">{{ drive.name }}</h5>
                <small class="text-muted">{{ drive.driveType }}</small>
            </div>
            {% if drive.description %}
            <p class="mb-1">{{ drive.description }}</p>
            {% endif %}
        </a>
        {% else %}
        <div class="alert alert-info">
            No document libraries found in this site.
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %} 