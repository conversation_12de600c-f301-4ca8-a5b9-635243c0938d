{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <h2 class="mb-4">Select a SharePoint Site</h2>
    
    <div class="list-group">
        {% for site in sites %}
        <a href="{{ url_for('list_sharepoint_drives', site_id=site.id) }}" class="list-group-item list-group-item-action">
            <div class="d-flex w-100 justify-content-between">
                <h5 class="mb-1">{{ site.displayName }}</h5>
            </div>
            <p class="mb-1 text-muted small">{{ site.webUrl }}</p>
        </a>
        {% else %}
        <div class="alert alert-info">
            No SharePoint sites found. Make sure you have access to at least one site.
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %} 