{% extends "base.html" %}

{% block title %}{{ error_title | default('Error') }}{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="alert alert-danger" role="alert">
        <h4 class="alert-heading">{{ error_title | default('An Error Occurred') }}</h4>
        <p>{{ error_message | default('Something went wrong. Please try again later or contact support.') }}</p>
        
        {% if error_details %}
        <hr>
        <p class="mb-0 small text-muted">
            Technical details: {{ error_details }}
        </p>
        {% endif %}
        
        <hr>
        <p class="mb-0">
            <a href="{{ url_for('root') }}" class="btn btn-secondary btn-sm">Go to Homepage</a>
            {% if request.headers.get('Referer') %}
            <a href="{{ request.headers.get('Referer') }}" class="btn btn-outline-secondary btn-sm">Go Back</a>
            {% endif %}
        </p>
    </div>
</div>
{% endblock %} 